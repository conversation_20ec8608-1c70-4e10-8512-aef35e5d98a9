import React, { useRef, useState, useCallback, useEffect } from "react";
import {
  Box,
  Button,
  Typography,
  LinearProgress,
  Chip,
  IconButton,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from "@mui/material";
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  AttachFile as AttachFileIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
} from "@mui/icons-material";

export type FileUploadMode = "single" | "multiple";
export type FileUploadVariant = "button" | "dropzone" | "inline";

export interface FileValidation {
  maxSize?: number; // in bytes
  minSize?: number; // in bytes
  allowedTypes?: string[]; // MIME types or extensions
  maxCount?: number;
}

export interface UploadFile {
  id: string;
  file: File;
  progress: number;
  status: "pending" | "uploading" | "success" | "error";
  url?: string;
  error?: string;
}

export interface FileUploadProps {
  mode?: FileUploadMode;
  variant?: FileUploadVariant;
  validation?: FileValidation;
  disabled?: boolean;
  placeholder?: string;
  description?: string;
  onFilesSelected?: (files: File[]) => void;
  onUploadProgress?: (fileId: string, progress: number) => void;
  onUploadComplete?: (fileId: string, result: any) => void;
  onUploadError?: (fileId: string, error: string) => void;
  onFileRemove?: (fileId: string) => void;
  customUploadHandler?: (file: File) => Promise<any>;
  showPreview?: boolean;
  accept?: string;
  children?: React.ReactNode;
}

const defaultValidation: FileValidation = {
  maxSize: 100 * 1024 * 1024, // 100MB
  minSize: 0,
  maxCount: 10,
};

export const UnifiedFileUpload: React.FC<FileUploadProps> = ({
  mode = "multiple",
  variant = "dropzone",
  validation = defaultValidation,
  disabled = false,
  placeholder = "Click to upload or drag and drop",
  description = "Support for single or bulk upload",
  onFilesSelected,
  onUploadProgress,
  onUploadComplete,
  onUploadError,
  onFileRemove,
  customUploadHandler,
  showPreview = true,
  accept,
  children,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showFilesDialog, setShowFilesDialog] = useState(false);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "warning";
  }>({ open: false, message: "", severity: "success" });

  // 验证文件
  const validateFile = useCallback(
    (file: File): string | null => {
      const { maxSize, minSize, allowedTypes } = validation;

      if (maxSize && file.size > maxSize) {
        return `File size exceeds ${(maxSize / 1024 / 1024).toFixed(
          1
        )}MB limit`;
      }

      if (minSize && file.size < minSize) {
        return `File size below ${(minSize / 1024).toFixed(1)}KB minimum`;
      }

      if (allowedTypes && allowedTypes.length > 0) {
        const isTypeAllowed = allowedTypes.some((type) => {
          if (type.startsWith(".")) {
            // Extension check
            return file.name.toLowerCase().endsWith(type.toLowerCase());
          } else {
            // MIME type check
            return file.type === type || file.type.startsWith(type);
          }
        });

        if (!isTypeAllowed) {
          return `File type not allowed. Supported types: ${allowedTypes.join(
            ", "
          )}`;
        }
      }

      return null;
    },
    [validation]
  );

  // 处理文件选择
  const handleFilesSelect = useCallback(
    (files: FileList) => {
      const newFiles: File[] = [];
      const errors: string[] = [];

      Array.from(files).forEach((file) => {
        const error = validateFile(file);
        if (error) {
          errors.push(`${file.name}: ${error}`);
        } else {
          newFiles.push(file);
        }
      });

      // 检查文件数量限制
      if (validation.maxCount) {
        const totalCount = uploadFiles.length + newFiles.length;
        if (totalCount > validation.maxCount) {
          errors.push(`Maximum ${validation.maxCount} files allowed`);
          return;
        }
      }

      // 单文件模式检查
      if (mode === "single" && newFiles.length > 1) {
        errors.push("Only one file is allowed");
        return;
      }

      if (errors.length > 0) {
        setNotification({
          open: true,
          message: errors.join("; "),
          severity: "error",
        });
        return;
      }

      if (newFiles.length > 0) {
        const uploadFilesData: UploadFile[] = newFiles.map((file) => ({
          id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          file,
          progress: 0,
          status: "pending",
        }));

        if (mode === "single") {
          setUploadFiles(uploadFilesData);
        } else {
          setUploadFiles((prev) => [...prev, ...uploadFilesData]);
        }

        onFilesSelected?.(newFiles);

        // 自动开始上传（如果有自定义上传处理器）
        if (customUploadHandler) {
          uploadFilesData.forEach((uploadFile) => {
            handleUpload(uploadFile);
          });
        }
      }
    },
    [
      validateFile,
      validation.maxCount,
      uploadFiles.length,
      mode,
      onFilesSelected,
      customUploadHandler,
    ]
  );

  // 上传文件
  const handleUpload = useCallback(
    async (uploadFile: UploadFile) => {
      if (!customUploadHandler) return;

      setUploadFiles((prev) =>
        prev.map((f) =>
          f.id === uploadFile.id ? { ...f, status: "uploading" } : f
        )
      );

      try {
        // 模拟进度更新
        const progressInterval = setInterval(() => {
          setUploadFiles((prev) =>
            prev.map((f) => {
              if (f.id === uploadFile.id && f.status === "uploading") {
                const newProgress = Math.min(
                  f.progress + Math.random() * 30,
                  90
                );
                onUploadProgress?.(f.id, newProgress);
                return { ...f, progress: newProgress };
              }
              return f;
            })
          );
        }, 500);

        const result = await customUploadHandler(uploadFile.file);

        clearInterval(progressInterval);

        setUploadFiles((prev) =>
          prev.map((f) =>
            f.id === uploadFile.id
              ? { ...f, progress: 100, status: "success", url: result.url }
              : f
          )
        );

        onUploadComplete?.(uploadFile.id, result);

        setNotification({
          open: true,
          message: `${uploadFile.file.name} uploaded successfully`,
          severity: "success",
        });
      } catch (error: any) {
        setUploadFiles((prev) =>
          prev.map((f) =>
            f.id === uploadFile.id
              ? { ...f, status: "error", error: error.message }
              : f
          )
        );

        onUploadError?.(uploadFile.id, error.message);

        setNotification({
          open: true,
          message: `Failed to upload ${uploadFile.file.name}: ${error.message}`,
          severity: "error",
        });
      }
    },
    [customUploadHandler, onUploadProgress, onUploadComplete, onUploadError]
  );

  // 重试上传
  const handleRetry = useCallback(
    (uploadFile: UploadFile) => {
      setUploadFiles((prev) =>
        prev.map((f) =>
          f.id === uploadFile.id
            ? { ...f, status: "pending", progress: 0, error: undefined }
            : f
        )
      );
      handleUpload(uploadFile);
    },
    [handleUpload]
  );

  // 移除文件
  const handleRemoveFile = useCallback(
    (fileId: string) => {
      setUploadFiles((prev) => prev.filter((f) => f.id !== fileId));
      onFileRemove?.(fileId);
    },
    [onFileRemove]
  );

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      if (disabled) return;

      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFilesSelect(files);
      }
    },
    [disabled, handleFilesSelect]
  );

  // 点击处理
  const handleClick = useCallback(() => {
    if (disabled) return;
    fileInputRef.current?.click();
  }, [disabled]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFilesSelect(files);
      }
      // 清空input value以允许重复选择同一文件
      e.target.value = "";
    },
    [handleFilesSelect]
  );

  // 获取文件类型图标
  const getFileTypeIcon = (file: File) => {
    if (file.type.startsWith("image/")) return "🖼️";
    if (file.type.startsWith("video/")) return "🎥";
    if (file.type.startsWith("audio/")) return "🎵";
    if (file.type.includes("pdf")) return "📄";
    return "📎";
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // 渲染上传区域
  const renderUploadArea = () => {
    if (variant === "button") {
      return (
        <Button
          variant="contained"
          startIcon={<CloudUploadIcon />}
          onClick={handleClick}
          disabled={disabled}
          sx={{
            textTransform: "none",
            minWidth: 120,
          }}
        >
          {children || "Upload"}
        </Button>
      );
    }

    if (variant === "inline") {
      return (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <IconButton onClick={handleClick} disabled={disabled} size="small">
            <AttachFileIcon />
          </IconButton>
          {uploadFiles.length > 0 && (
            <Chip
              label={`${uploadFiles.length} file(s)`}
              size="small"
              onClick={() => setShowFilesDialog(true)}
            />
          )}
        </Box>
      );
    }

    // Dropzone variant
    return (
      <Box
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        sx={{
          border: "2px dashed",
          borderColor: isDragOver ? "primary.main" : "grey.300",
          borderRadius: 2,
          p: 3,
          textAlign: "center",
          cursor: disabled ? "default" : "pointer",
          backgroundColor: isDragOver ? "action.hover" : "transparent",
          transition: "all 0.2s ease",
          "&:hover": disabled
            ? {}
            : {
                borderColor: "primary.main",
                backgroundColor: "action.hover",
              },
        }}
      >
        <CloudUploadIcon
          sx={{
            fontSize: 48,
            color: "grey.400",
            mb: 2,
          }}
        />
        <Typography variant="h6" gutterBottom>
          {placeholder}
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {description}
        </Typography>
        {validation.allowedTypes && (
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            Supported formats: {validation.allowedTypes.join(", ")}
          </Typography>
        )}
        {validation.maxSize && (
          <Typography variant="caption" display="block">
            Max file size: {(validation.maxSize / 1024 / 1024).toFixed(1)}MB
          </Typography>
        )}
      </Box>
    );
  };

  // 渲染文件列表
  const renderFileList = () => {
    if (!showPreview || uploadFiles.length === 0) return null;

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Files ({uploadFiles.length})
        </Typography>
        {uploadFiles.slice(0, 3).map((uploadFile) => (
          <Box
            key={uploadFile.id}
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              p: 1,
              border: "1px solid",
              borderColor: "divider",
              borderRadius: 1,
              mb: 1,
            }}
          >
            <Typography sx={{ fontSize: "1.2em" }}>
              {getFileTypeIcon(uploadFile.file)}
            </Typography>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography variant="body2" noWrap>
                {uploadFile.file.name}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {formatFileSize(uploadFile.file.size)}
              </Typography>
              {uploadFile.status === "uploading" && (
                <LinearProgress
                  variant="determinate"
                  value={uploadFile.progress}
                  sx={{ mt: 0.5, height: 4 }}
                />
              )}
            </Box>
            {uploadFile.status === "success" && (
              <CheckCircleIcon color="success" fontSize="small" />
            )}
            {uploadFile.status === "error" && (
              <IconButton
                size="small"
                onClick={() => handleRetry(uploadFile)}
                title="Retry upload"
              >
                <RefreshIcon fontSize="small" />
              </IconButton>
            )}
            <IconButton
              size="small"
              onClick={() => handleRemoveFile(uploadFile.id)}
              disabled={uploadFile.status === "uploading"}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Box>
        ))}

        {uploadFiles.length > 3 && (
          <Button size="small" onClick={() => setShowFilesDialog(true)}>
            View all files ({uploadFiles.length})
          </Button>
        )}
      </Box>
    );
  };

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        multiple={mode === "multiple"}
        accept={accept}
        onChange={handleInputChange}
        style={{ display: "none" }}
      />

      {renderUploadArea()}
      {renderFileList()}

      {/* 文件详情对话框 */}
      <Dialog
        open={showFilesDialog}
        onClose={() => setShowFilesDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Upload Files ({uploadFiles.length})</DialogTitle>
        <DialogContent>
          <List>
            {uploadFiles.map((uploadFile) => (
              <ListItem key={uploadFile.id}>
                <ListItemText
                  primary={
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <span>{getFileTypeIcon(uploadFile.file)}</span>
                      <span>{uploadFile.file.name}</span>
                      {uploadFile.status === "success" && (
                        <CheckCircleIcon color="success" fontSize="small" />
                      )}
                      {uploadFile.status === "error" && (
                        <ErrorIcon color="error" fontSize="small" />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption">
                        {formatFileSize(uploadFile.file.size)} •{" "}
                        {uploadFile.status}
                      </Typography>
                      {uploadFile.status === "uploading" && (
                        <LinearProgress
                          variant="determinate"
                          value={uploadFile.progress}
                          sx={{ mt: 0.5 }}
                        />
                      )}
                      {uploadFile.error && (
                        <Typography
                          variant="caption"
                          color="error"
                          display="block"
                        >
                          {uploadFile.error}
                        </Typography>
                      )}
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  {uploadFile.status === "error" && (
                    <IconButton
                      edge="end"
                      onClick={() => handleRetry(uploadFile)}
                      title="Retry upload"
                    >
                      <RefreshIcon />
                    </IconButton>
                  )}
                  <IconButton
                    edge="end"
                    onClick={() => handleRemoveFile(uploadFile.id)}
                    disabled={uploadFile.status === "uploading"}
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowFilesDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* 通知 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={4000}
        onClose={() => setNotification((prev) => ({ ...prev, open: false }))}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification((prev) => ({ ...prev, open: false }))}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default UnifiedFileUpload;
